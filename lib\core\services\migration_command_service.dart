import 'dart:developer';
import 'package:dartz/dartz.dart';
import '../../models/failure_obj.dart';
import 'voucher_chart_of_accounts_migration_service.dart';

/// Service for executing migration commands and managing migration lifecycle
class MigrationCommandService {
  final VoucherChartOfAccountsMigrationService _voucherMigrationService = 
      VoucherChartOfAccountsMigrationService();

  /// Execute voucher migration with comprehensive logging and error handling
  Future<Either<FailureObj, String>> executeVoucherMigration({
    required String uid,
    bool dryRun = false,
    bool force = false,
  }) async {
    try {
      log('=== STARTING VOUCHER MIGRATION ===');
      log('Company UID: $uid');
      log('Dry Run: $dryRun');
      log('Force: $force');

      // Step 1: Check if migration is needed (unless forced)
      if (!force) {
        log('Checking if migration is needed...');
        final migrationNeededResult = await _voucherMigrationService.checkMigrationNeeded(uid);
        
        final migrationNeeded = migrationNeededResult.fold(
          (failure) {
            log('Failed to check migration status: ${failure.message}');
            return true; // Assume migration is needed if check fails
          },
          (needed) => needed,
        );

        if (!migrationNeeded) {
          log('Migration not needed - all vouchers already migrated');
          return const Right('Migration not needed - all vouchers already use Chart of Accounts');
        }
      }

      // Step 2: Execute migration
      log('Starting migration process...');
      final migrationResult = await _voucherMigrationService.migrateVouchersToChartOfAccounts(
        uid: uid,
        dryRun: dryRun,
        onProgress: (progress) {
          log('Migration Progress: $progress');
        },
      );

      return migrationResult.fold(
        (failure) {
          log('Migration failed: ${failure.message}');
          return Left(failure);
        },
        (result) {
          log('=== MIGRATION COMPLETED ===');
          log(result.summary);
          
          if (result.errors.isNotEmpty) {
            log('ERRORS ENCOUNTERED:');
            for (final error in result.errors) {
              log('- $error');
            }
          }
          
          if (result.warnings.isNotEmpty) {
            log('WARNINGS:');
            for (final warning in result.warnings) {
              log('- $warning');
            }
          }

          final statusMessage = dryRun 
              ? 'Dry run completed: ${result.migratedVouchers} vouchers would be migrated'
              : 'Migration completed: ${result.migratedVouchers} vouchers migrated successfully';

          return Right(statusMessage);
        },
      );

    } catch (e) {
      log('Unexpected error during migration: $e');
      return Left(FailureObj(
        code: 'migration-execution-failed',
        message: 'Migration execution failed: $e',
      ));
    }
  }

  /// Check migration status for a company
  Future<Either<FailureObj, MigrationStatus>> checkMigrationStatus(String uid) async {
    try {
      log('Checking migration status for company: $uid');

      final migrationNeededResult = await _voucherMigrationService.checkMigrationNeeded(uid);
      
      return migrationNeededResult.fold(
        (failure) => Left(failure),
        (migrationNeeded) {
          final status = MigrationStatus(
            companyUid: uid,
            migrationNeeded: migrationNeeded,
            checkedAt: DateTime.now(),
          );
          
          log('Migration status: ${migrationNeeded ? 'NEEDED' : 'NOT NEEDED'}');
          return Right(status);
        },
      );

    } catch (e) {
      log('Error checking migration status: $e');
      return Left(FailureObj(
        code: 'migration-status-check-failed',
        message: 'Failed to check migration status: $e',
      ));
    }
  }

  /// Rollback migration for a specific voucher
  Future<Either<FailureObj, String>> rollbackVoucherMigration({
    required String uid,
    required String voucherNumber,
  }) async {
    try {
      log('Rolling back migration for voucher: $voucherNumber');

      final rollbackResult = await _voucherMigrationService.rollbackVoucherMigration(
        uid: uid,
        voucherNumber: voucherNumber,
      );

      return rollbackResult.fold(
        (failure) {
          log('Rollback failed: ${failure.message}');
          return Left(failure);
        },
        (message) {
          log('Rollback successful: $message');
          return Right(message);
        },
      );

    } catch (e) {
      log('Error during rollback: $e');
      return Left(FailureObj(
        code: 'rollback-failed',
        message: 'Rollback failed: $e',
      ));
    }
  }

  /// Generate comprehensive migration report
  Future<Either<FailureObj, String>> generateMigrationReport({
    required String uid,
    bool includeAccountMappings = true,
  }) async {
    try {
      log('Generating migration report for company: $uid');

      // Run a dry run to get migration data
      final migrationResult = await _voucherMigrationService.migrateVouchersToChartOfAccounts(
        uid: uid,
        dryRun: true,
      );

      return migrationResult.fold(
        (failure) => Left(failure),
        (result) {
          final report = _voucherMigrationService.generateMigrationReport(result);
          log('Migration report generated successfully');
          return Right(report);
        },
      );

    } catch (e) {
      log('Error generating migration report: $e');
      return Left(FailureObj(
        code: 'report-generation-failed',
        message: 'Failed to generate migration report: $e',
      ));
    }
  }
}

/// Migration status model
class MigrationStatus {
  final String companyUid;
  final bool migrationNeeded;
  final DateTime checkedAt;

  MigrationStatus({
    required this.companyUid,
    required this.migrationNeeded,
    required this.checkedAt,
  });

  String get statusText => migrationNeeded 
      ? 'Migration Required' 
      : 'Migration Complete';

  String get description => migrationNeeded
      ? 'Some vouchers still use legacy account references and need to be migrated to Chart of Accounts'
      : 'All vouchers are using Chart of Accounts structure';

  Map<String, dynamic> toJson() => {
    'companyUid': companyUid,
    'migrationNeeded': migrationNeeded,
    'checkedAt': checkedAt.toIso8601String(),
    'statusText': statusText,
    'description': description,
  };

  @override
  String toString() {
    return 'MigrationStatus(uid: $companyUid, needed: $migrationNeeded, checked: $checkedAt)';
  }
}
