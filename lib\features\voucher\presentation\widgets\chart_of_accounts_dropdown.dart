import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../models/finance/chart_of_accounts_model.dart';
import '../../../accounting/chart_of_accounts/presentation/controllers/chart_of_accounts_controller.dart';

/// Reusable dropdown widget for Chart of Accounts selection
/// Replaces legacy AccountModel dropdowns in voucher system
class ChartOfAccountsDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final Function(ChartOfAccountsModel?) onChanged;
  final List<AccountCategory>? allowedCategories;
  final List<AccountType>? allowedTypes;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;
  final bool showAccountNumber;
  final bool showBalance;
  final bool activeOnly;

  const ChartOfAccountsDropdown({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.selectedAccount,
    required this.onChanged,
    this.allowedCategories,
    this.allowedTypes,
    this.isRequired = false,
    this.validator,
    this.showAccountNumber = true,
    this.showBalance = false,
    this.activeOnly = true,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ChartOfAccountsController>();

    return Obx(() {
      // Filter accounts based on criteria
      final filteredAccounts = _filterAccounts(controller.allAccounts);

      return DropdownButtonFormField<ChartOfAccountsModel>(
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          filled: true,
          fillColor: Colors.transparent,
          prefixIcon: Icon(
            _getCategoryIcon(),
            color: Theme.of(context).primaryColor,
          ),
        ),
        value: selectedAccount,
        validator: validator ??
            (value) {
              if (isRequired && value == null) {
                return 'Please select an account';
              }
              return null;
            },
        onChanged: onChanged,
        items: filteredAccounts.map((account) {
          return DropdownMenuItem<ChartOfAccountsModel>(
            value: account,
            child: _buildAccountItem(account, context),
          );
        }).toList(),
        isExpanded: true,
        menuMaxHeight: 300,
      );
    });
  }

  /// Filter accounts based on allowed categories and types
  List<ChartOfAccountsModel> _filterAccounts(List<ChartOfAccountsModel> accounts) {
    return accounts.where((account) {
      // Filter by active status
      if (activeOnly && !account.isActive) return false;

      // Filter by allowed categories
      if (allowedCategories != null && !allowedCategories!.contains(account.category)) {
        return false;
      }

      // Filter by allowed types
      if (allowedTypes != null && !allowedTypes!.contains(account.accountType)) {
        return false;
      }

      return true;
    }).toList();
  }

  /// Build individual account item widget
  Widget _buildAccountItem(ChartOfAccountsModel account, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            // Account status indicator
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: account.isActive ? Colors.green : Colors.grey,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            // Account number and name
            Expanded(
              child: Text(
                showAccountNumber 
                    ? account.displayName 
                    : account.accountName,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // Balance display (if enabled)
            if (showBalance) ...[
              const SizedBox(width: 8),
              Text(
                '\$${account.balance.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 12,
                  color: account.balance >= 0 ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
        // Account type and category
        Padding(
          padding: const EdgeInsets.only(left: 16, top: 2),
          child: Text(
            '${account.accountType.displayName} • ${account.category.displayName}',
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey[600],
            ),
          ),
        ),
      ],
    );
  }

  /// Get appropriate icon based on allowed categories
  IconData _getCategoryIcon() {
    if (allowedCategories == null || allowedCategories!.isEmpty) {
      return Icons.account_balance;
    }

    // Return icon for the first allowed category
    return allowedCategories!.first.icon;
  }
}

/// Specialized dropdown for expense accounts (broker fees, munshiana, etc.)
class ExpenseAccountDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final Function(ChartOfAccountsModel?) onChanged;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;

  const ExpenseAccountDropdown({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.selectedAccount,
    required this.onChanged,
    this.isRequired = false,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return ChartOfAccountsDropdown(
      labelText: labelText,
      hintText: hintText,
      selectedAccount: selectedAccount,
      onChanged: onChanged,
      allowedCategories: const [AccountCategory.expenses],
      isRequired: isRequired,
      validator: validator,
      showAccountNumber: true,
      showBalance: false,
      activeOnly: true,
    );
  }
}

/// Specialized dropdown for asset accounts (cash, bank accounts for payments)
class AssetAccountDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final Function(ChartOfAccountsModel?) onChanged;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;

  const AssetAccountDropdown({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.selectedAccount,
    required this.onChanged,
    this.isRequired = false,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return ChartOfAccountsDropdown(
      labelText: labelText,
      hintText: hintText,
      selectedAccount: selectedAccount,
      onChanged: onChanged,
      allowedCategories: const [AccountCategory.assets],
      allowedTypes: const [
        AccountType.cash,
        AccountType.bank,
        AccountType.currentAssets,
      ],
      isRequired: isRequired,
      validator: validator,
      showAccountNumber: true,
      showBalance: true,
      activeOnly: true,
    );
  }
}

/// Specialized dropdown for revenue accounts
class RevenueAccountDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final Function(ChartOfAccountsModel?) onChanged;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;

  const RevenueAccountDropdown({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.selectedAccount,
    required this.onChanged,
    this.isRequired = false,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return ChartOfAccountsDropdown(
      labelText: labelText,
      hintText: hintText,
      selectedAccount: selectedAccount,
      onChanged: onChanged,
      allowedCategories: const [AccountCategory.revenue],
      isRequired: isRequired,
      validator: validator,
      showAccountNumber: true,
      showBalance: false,
      activeOnly: true,
    );
  }
}
