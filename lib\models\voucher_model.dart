class VoucherModel {
  String voucherNumber;
  String voucherStatus;
  int weightInTons;
  List<String> invoiceTasNumberList;
  List<String> invoiceBiltyNumberList;
  DateTime? belongsToDate; // Date this voucher belongs to (user-selected)
  DateTime createdAt; // System creation date

  String productName;
  int totalNumberOfBags;
  String brokerType;
  String brokerName;
  String selectedBroker; // For persistent broker selection
  double brokerFees;
  double munshianaFees;
  String brokerAccount; // Legacy field - deprecated, use brokerAccountId
  String munshianaAccount; // Legacy field - deprecated, use munshianaAccountId

  // Chart of Accounts Integration
  String? brokerAccountId; // Chart of Accounts ID for broker fees
  String? munshianaAccountId; // Chart of Accounts ID for munshiana fees
  String? salesTaxAccountId; // Chart of Accounts ID for sales tax (4.6% tax)
  String? freightTaxAccountId; // Chart of Accounts ID for freight tax (15% tax)
  String? profitAccountId; // Chart of Accounts ID for profit allocation

  // Enhanced broker management
  List<String> brokerList; // List of broker IDs associated with this voucher

  // Tax and profit management
  double calculatedProfit;
  double calculatedTax;
  double calculatedFreightTax; // 15% tax on Total Freight (NLC amount)

  // Legacy account name fields - deprecated, use Chart of Accounts for names
  String? taxAccountName;
  String? freightTaxAccountName;
  String? profitAccountName;
  String? companyFreightAccountId;
  String? companyFreightAccountName;
  String? brokerCompanyId;
  String? brokerCompanyName;
  String driverName;
  String driverPhoneNumber;
  String truckNumber;
  String conveyNoteNumber;
  double totalFreight;
  double companyFreight;
  double settledFreight;
  String departureDate;

  List<Map<String, dynamic>> paymentTransactions;

  // Payment options
  double? dieselLiters;
  String? dieselCompany;
  double? chequeAmount;
  String? bankName;
  String? chequeNumber;

  // Tax Authority Selection (15% tax options)
  List<String> selectedTaxAuthorities;

  // Constructor
  VoucherModel({
    required this.voucherStatus,
    required this.departureDate,
    required this.driverName,
    required this.invoiceTasNumberList,
    required this.invoiceBiltyNumberList,
    required this.weightInTons,
    required this.voucherNumber,
    required this.productName,
    required this.totalNumberOfBags,
    required this.brokerType,
    required this.brokerName,
    this.selectedBroker = '', // Default empty string
    required this.brokerFees,
    required this.munshianaFees,
    required this.brokerAccount,
    required this.munshianaAccount,
    required this.driverPhoneNumber,
    required this.truckNumber,
    required this.conveyNoteNumber,
    required this.totalFreight,
    this.companyFreight = 0.0,
    this.settledFreight = 0.0,
    this.paymentTransactions = const [],
    this.dieselLiters,
    this.dieselCompany,
    this.chequeAmount,
    this.bankName,
    this.chequeNumber,
    this.belongsToDate,
    DateTime? createdAt,
    // New fields with defaults
    this.brokerList = const [],
    this.calculatedProfit = 0.0,
    this.calculatedTax = 0.0,
    this.calculatedFreightTax = 0.0,
    // Chart of Accounts fields
    this.brokerAccountId,
    this.munshianaAccountId,
    this.salesTaxAccountId,
    this.freightTaxAccountId,
    this.profitAccountId,
    // Legacy account name fields
    this.taxAccountName,
    this.freightTaxAccountName,
    this.profitAccountName,
    this.companyFreightAccountId,
    this.companyFreightAccountName,
    this.brokerCompanyId,
    this.brokerCompanyName,
    this.selectedTaxAuthorities = const [],
  }) : createdAt = createdAt ?? DateTime.now();

  // Method to convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'driverName': driverName,
      'voucherNumber': voucherNumber,
      'departureDate': departureDate,
      'productName': productName,
      'totalNumberOfBags': totalNumberOfBags,
      'brokerType': brokerType,
      'brokerName': brokerName,
      'selectedBroker': selectedBroker,
      'brokerFees': brokerFees,
      'munshianaFees': munshianaFees,
      'brokerAccount': brokerAccount,
      'munshianaAccount': munshianaAccount,
      'driverPhoneNumber': driverPhoneNumber,
      'truckNumber': truckNumber,
      'conveyNoteNumber': conveyNoteNumber,
      'totalFreight': totalFreight,
      'companyFreight': companyFreight,
      'settledFreight': settledFreight,
      'paymentTransactions': paymentTransactions,
      'weightInTons': weightInTons,
      'invoiceTasNumberList': invoiceTasNumberList,
      'invoiceBiltyNumberList': invoiceBiltyNumberList,
      'voucherStatus': voucherStatus,
      'dieselLiters': dieselLiters,
      'dieselCompany': dieselCompany,
      'chequeAmount': chequeAmount,
      'bankName': bankName,
      'chequeNumber': chequeNumber,
      'belongsToDate': belongsToDate?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      // New fields
      'brokerList': brokerList,
      'calculatedProfit': calculatedProfit,
      'calculatedTax': calculatedTax,
      'calculatedFreightTax': calculatedFreightTax,
      // Chart of Accounts fields
      'brokerAccountId': brokerAccountId,
      'munshianaAccountId': munshianaAccountId,
      'salesTaxAccountId': salesTaxAccountId,
      'freightTaxAccountId': freightTaxAccountId,
      'profitAccountId': profitAccountId,
      // Legacy account name fields
      'taxAccountName': taxAccountName,
      'freightTaxAccountName': freightTaxAccountName,
      'profitAccountName': profitAccountName,
      'companyFreightAccountId': companyFreightAccountId,
      'companyFreightAccountName': companyFreightAccountName,
      'brokerCompanyId': brokerCompanyId,
      'brokerCompanyName': brokerCompanyName,
      'selectedTaxAuthorities': selectedTaxAuthorities,
    };
  }

  // Factory to create an instance from JSON
  factory VoucherModel.fromJson(Map<String, dynamic> json) {
    return VoucherModel(
      voucherStatus: json['voucherStatus']?.toString() ?? '',
      driverName: json['driverName']?.toString() ?? '',
      invoiceTasNumberList: json['invoiceTasNumberList'] != null
          ? List<String>.from(
              json['invoiceTasNumberList'].map((e) => e.toString()))
          : [],
      invoiceBiltyNumberList: json['invoiceBiltyNumberList'] != null
          ? List<String>.from(
              json['invoiceBiltyNumberList'].map((e) => e.toString()))
          : [],
      weightInTons: json['weightInTons'] is int
          ? json['weightInTons']
          : int.tryParse(json['weightInTons']?.toString() ?? '0') ?? 0,
      voucherNumber: json['voucherNumber']?.toString() ?? '',
      departureDate: json['departureDate']?.toString() ?? '',
      productName: json['productName']?.toString() ?? '',
      totalNumberOfBags: json['totalNumberOfBags'] is int
          ? json['totalNumberOfBags']
          : int.tryParse(json['totalNumberOfBags']?.toString() ?? '0') ?? 0,
      brokerType: json['brokerType']?.toString() ?? 'Own',
      brokerName: json['brokerName']?.toString() ?? '',
      selectedBroker: json['selectedBroker']?.toString() ?? '',
      brokerFees: json['brokerFees'] != null
          ? (json['brokerFees'] as num).toDouble()
          : 0.0,
      munshianaFees: json['munshianaFees'] != null
          ? (json['munshianaFees'] as num).toDouble()
          : 0.0,
      brokerAccount: json['brokerAccount']?.toString() ?? '',
      munshianaAccount: json['munshianaAccount']?.toString() ?? '',
      driverPhoneNumber: json['driverPhoneNumber']?.toString() ?? '',
      truckNumber: json['truckNumber']?.toString() ?? '',
      conveyNoteNumber: json['conveyNoteNumber']?.toString() ?? '',
      totalFreight: json['totalFreight'] != null
          ? (json['totalFreight'] as num).toDouble()
          : 0.0,
      companyFreight: json['companyFreight'] != null
          ? (json['companyFreight'] as num).toDouble()
          : 0.0,
      settledFreight: json['settledFreight'] != null
          ? (json['settledFreight'] as num).toDouble()
          : 0.0,
      paymentTransactions: json['paymentTransactions'] != null
          ? List<Map<String, dynamic>>.from(json['paymentTransactions'])
          : [],
      dieselLiters: json['dieselLiters'] != null
          ? (json['dieselLiters'] as num).toDouble()
          : null,
      dieselCompany: json['dieselCompany']?.toString(),
      chequeAmount: json['chequeAmount'] != null
          ? (json['chequeAmount'] as num).toDouble()
          : null,
      bankName: json['bankName']?.toString(),
      chequeNumber: json['chequeNumber']?.toString(),
      belongsToDate: json['belongsToDate'] != null
          ? _parseDateTime(json['belongsToDate'])
          : null,
      createdAt: json['createdAt'] != null
          ? _parseDateTime(json['createdAt'])
          : DateTime.now(),
      // New fields
      brokerList: json['brokerList'] != null
          ? List<String>.from(json['brokerList'].map((e) => e.toString()))
          : [],
      calculatedProfit: json['calculatedProfit'] != null
          ? (json['calculatedProfit'] as num).toDouble()
          : 0.0,
      calculatedTax: json['calculatedTax'] != null
          ? (json['calculatedTax'] as num).toDouble()
          : 0.0,
      calculatedFreightTax: json['calculatedFreightTax'] != null
          ? (json['calculatedFreightTax'] as num).toDouble()
          : 0.0,
      // Chart of Accounts fields
      brokerAccountId: json['brokerAccountId']?.toString(),
      munshianaAccountId: json['munshianaAccountId']?.toString(),
      salesTaxAccountId: json['salesTaxAccountId']?.toString() ??
          json['taxAccountId']?.toString(), // Backward compatibility
      freightTaxAccountId: json['freightTaxAccountId']?.toString(),
      profitAccountId: json['profitAccountId']?.toString(),
      // Legacy account name fields
      taxAccountName: json['taxAccountName']?.toString(),
      freightTaxAccountName: json['freightTaxAccountName']?.toString(),
      profitAccountName: json['profitAccountName']?.toString(),
      companyFreightAccountId: json['companyFreightAccountId']?.toString(),
      companyFreightAccountName: json['companyFreightAccountName']?.toString(),
      brokerCompanyId: json['brokerCompanyId']?.toString(),
      brokerCompanyName: json['brokerCompanyName']?.toString(),
      selectedTaxAuthorities: json['selectedTaxAuthorities'] != null
          ? List<String>.from(
              json['selectedTaxAuthorities'].map((e) => e.toString()))
          : [],
    );
  }

  // Helper method to safely parse DateTime from various formats
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;

    try {
      // If it's already a DateTime, return it
      if (value is DateTime) return value;

      // If it's a timestamp (int), convert it
      if (value is int) {
        return DateTime.fromMillisecondsSinceEpoch(value);
      }

      // If it's a string, try to parse it
      if (value is String) {
        // Try parsing as ISO string first
        try {
          return DateTime.parse(value);
        } catch (e) {
          // If that fails, try parsing as timestamp
          final timestamp = int.tryParse(value);
          if (timestamp != null) {
            return DateTime.fromMillisecondsSinceEpoch(timestamp);
          }
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  double get pendingAmount => totalFreight - settledFreight;

  void addPaymentTransaction(Map<String, dynamic> transaction) {
    paymentTransactions.add(transaction);
    if (transaction['amount'] != null) {
      settledFreight += (transaction['amount'] as num).toDouble();
    }
  }

  @override
  String toString() {
    return '''
Voucher(
  driverName: $driverName,
  invoiceTasNumberList: $invoiceTasNumberList,
  invoiceBiltyNumberList: $invoiceBiltyNumberList,
  weightInTons: $weightInTons,
  voucherNumber: $voucherNumber,
  departureDate: $departureDate,
  productName: $productName,
  totalNumberOfBags: $totalNumberOfBags,
  brokerType: $brokerType,
  brokerName: $brokerName,
  selectedBroker: $selectedBroker,
  brokerFees: $brokerFees,
  munshianaFees: $munshianaFees,
  brokerAccount: $brokerAccount,
  munshianaAccount: $munshianaAccount,
  driverPhoneNumber: $driverPhoneNumber,
  truckNumber: $truckNumber,
  conveyNoteNumber: $conveyNoteNumber,
  totalFreight: $totalFreight,
  settledFreight: $settledFreight,
  pendingAmount: $pendingAmount,
  paymentTransactions: $paymentTransactions,
  voucherStatus: $voucherStatus,
  dieselLiters: $dieselLiters,
  dieselCompany: $dieselCompany,
  chequeAmount: $chequeAmount,
  bankName: $bankName,
  chequeNumber: $chequeNumber,
  belongsToDate: $belongsToDate,
  createdAt: $createdAt,
  brokerList: $brokerList,
  calculatedProfit: $calculatedProfit,
  calculatedTax: $calculatedTax,
  calculatedFreightTax: $calculatedFreightTax,
  brokerAccountId: $brokerAccountId,
  munshianaAccountId: $munshianaAccountId,
  salesTaxAccountId: $salesTaxAccountId,
  freightTaxAccountId: $freightTaxAccountId,
  profitAccountId: $profitAccountId,
  taxAccountName: $taxAccountName,
  freightTaxAccountName: $freightTaxAccountName,
  profitAccountName: $profitAccountName,
  companyFreightAccountId: $companyFreightAccountId,
  companyFreightAccountName: $companyFreightAccountName,
  brokerCompanyId: $brokerCompanyId,
  brokerCompanyName: $brokerCompanyName,
  selectedTaxAuthorities: $selectedTaxAuthorities,
)
''';
  }
}
