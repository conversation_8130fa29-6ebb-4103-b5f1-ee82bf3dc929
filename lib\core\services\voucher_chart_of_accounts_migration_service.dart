import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import '../../models/failure_obj.dart';
import '../../models/voucher_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../firebase_service/voucher/voucher_crud_firebase_service.dart';
import '../../firebase_service/finance/chart_of_accounts_firebase_service.dart';
import '../../core/constants/app_collection.dart';

/// Service to handle migration of voucher data from legacy account references
/// to Chart of Accounts IDs, ensuring data integrity and backward compatibility
class VoucherChartOfAccountsMigrationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final VoucherCrudFirebaseService _voucherService = VoucherCrudFirebaseService();
  final ChartOfAccountsFirebaseService _chartOfAccountsService = ChartOfAccountsFirebaseService();

  /// Migration result model
  class MigrationResult {
    final bool isSuccess;
    final int totalVouchers;
    final int migratedVouchers;
    final int skippedVouchers;
    final List<String> errors;
    final List<String> warnings;
    final Map<String, String> accountMappings;

    MigrationResult({
      required this.isSuccess,
      required this.totalVouchers,
      required this.migratedVouchers,
      required this.skippedVouchers,
      required this.errors,
      required this.warnings,
      required this.accountMappings,
    });

    String get summary => '''
Migration Summary:
- Total Vouchers: $totalVouchers
- Successfully Migrated: $migratedVouchers
- Skipped (already migrated): $skippedVouchers
- Errors: ${errors.length}
- Warnings: ${warnings.length}
- Account Mappings Created: ${accountMappings.length}
''';
  }

  /// Migrate all vouchers for a company from legacy accounts to Chart of Accounts
  Future<Either<FailureObj, MigrationResult>> migrateVouchersToChartOfAccounts({
    required String uid,
    Function(String)? onProgress,
    bool dryRun = false,
  }) async {
    try {
      log('Starting voucher migration to Chart of Accounts for company: $uid');
      onProgress?.call('Initializing migration...');

      // Step 1: Load all Chart of Accounts for mapping
      final chartOfAccountsResult = await _chartOfAccountsService.getChartOfAccounts();
      if (chartOfAccountsResult.isLeft()) {
        return Left(FailureObj(
          code: 'chart-of-accounts-load-failed',
          message: 'Failed to load Chart of Accounts for migration',
        ));
      }

      final chartOfAccounts = chartOfAccountsResult.fold(
        (failure) => <ChartOfAccountsModel>[],
        (accounts) => accounts,
      );

      log('Loaded ${chartOfAccounts.length} Chart of Accounts for mapping');

      // Step 2: Create account name to ID mapping
      onProgress?.call('Creating account mappings...');
      final accountMappings = _createAccountMappings(chartOfAccounts);
      log('Created ${accountMappings.length} account mappings');

      // Step 3: Load all vouchers for the company
      onProgress?.call('Loading vouchers...');
      final vouchers = await _loadAllVouchers(uid);
      log('Loaded ${vouchers.length} vouchers for migration');

      // Step 4: Analyze vouchers and perform migration
      onProgress?.call('Analyzing vouchers...');
      final migrationResult = await _performMigration(
        vouchers: vouchers,
        accountMappings: accountMappings,
        uid: uid,
        dryRun: dryRun,
        onProgress: onProgress,
      );

      log('Migration completed: ${migrationResult.summary}');
      return Right(migrationResult);

    } catch (e) {
      log('Error during voucher migration: $e');
      return Left(FailureObj(
        code: 'migration-failed',
        message: 'Voucher migration failed: $e',
      ));
    }
  }

  /// Create mapping from legacy account names to Chart of Accounts IDs
  Map<String, String> _createAccountMappings(List<ChartOfAccountsModel> chartOfAccounts) {
    final Map<String, String> mappings = {};

    for (final account in chartOfAccounts) {
      // Map by account name (case-insensitive)
      final accountName = account.accountName.toLowerCase().trim();
      mappings[accountName] = account.id;

      // Also map by account code if available
      if (account.accountCode.isNotEmpty) {
        mappings[account.accountCode.toLowerCase().trim()] = account.id;
      }
    }

    return mappings;
  }

  /// Load all vouchers for a company
  Future<List<Map<String, dynamic>>> _loadAllVouchers(String uid) async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppCollection.vouchersCollection)
          .where('uid', isEqualTo: uid)
          .get();

      return querySnapshot.docs
          .map((doc) => {...doc.data() as Map<String, dynamic>, 'id': doc.id})
          .toList();
    } catch (e) {
      log('Error loading vouchers: $e');
      rethrow;
    }
  }

  /// Perform the actual migration
  Future<MigrationResult> _performMigration({
    required List<Map<String, dynamic>> vouchers,
    required Map<String, String> accountMappings,
    required String uid,
    required bool dryRun,
    Function(String)? onProgress,
  }) async {
    int totalVouchers = vouchers.length;
    int migratedVouchers = 0;
    int skippedVouchers = 0;
    List<String> errors = [];
    List<String> warnings = [];

    for (int i = 0; i < vouchers.length; i++) {
      final voucherData = vouchers[i];
      final voucherNumber = voucherData['voucherNumber'] ?? 'Unknown';

      try {
        onProgress?.call('Processing voucher $voucherNumber (${i + 1}/$totalVouchers)');

        // Check if voucher already has Chart of Accounts fields
        if (_hasChartOfAccountsFields(voucherData)) {
          skippedVouchers++;
          log('Skipping voucher $voucherNumber - already has Chart of Accounts fields');
          continue;
        }

        // Migrate the voucher
        final migrationResult = _migrateVoucherData(voucherData, accountMappings);
        
        if (migrationResult.hasChanges) {
          if (!dryRun) {
            // Update the voucher in Firestore
            await _updateVoucherInFirestore(voucherData['id'], migrationResult.updatedData, uid);
          }
          migratedVouchers++;
          
          if (migrationResult.warnings.isNotEmpty) {
            warnings.addAll(migrationResult.warnings.map((w) => '$voucherNumber: $w'));
          }
          
          log('${dryRun ? '[DRY RUN] ' : ''}Migrated voucher $voucherNumber');
        } else {
          skippedVouchers++;
          log('Skipped voucher $voucherNumber - no legacy account fields found');
        }

      } catch (e) {
        errors.add('$voucherNumber: $e');
        log('Error migrating voucher $voucherNumber: $e');
      }
    }

    return MigrationResult(
      isSuccess: errors.isEmpty,
      totalVouchers: totalVouchers,
      migratedVouchers: migratedVouchers,
      skippedVouchers: skippedVouchers,
      errors: errors,
      warnings: warnings,
      accountMappings: accountMappings,
    );
  }

  /// Check if voucher already has Chart of Accounts fields
  bool _hasChartOfAccountsFields(Map<String, dynamic> voucherData) {
    return voucherData.containsKey('brokerAccountId') ||
           voucherData.containsKey('munshianaAccountId') ||
           voucherData.containsKey('salesTaxAccountId') ||
           voucherData.containsKey('freightTaxAccountId') ||
           voucherData.containsKey('profitAccountId');
  }

  /// Migration result for a single voucher
  class VoucherMigrationResult {
    final bool hasChanges;
    final Map<String, dynamic> updatedData;
    final List<String> warnings;

    VoucherMigrationResult({
      required this.hasChanges,
      required this.updatedData,
      required this.warnings,
    });
  }

  /// Migrate a single voucher's data
  VoucherMigrationResult _migrateVoucherData(
    Map<String, dynamic> voucherData,
    Map<String, String> accountMappings,
  ) {
    final updatedData = Map<String, dynamic>.from(voucherData);
    final warnings = <String>[];
    bool hasChanges = false;

    // Migrate broker account
    if (voucherData.containsKey('brokerAccount') && voucherData['brokerAccount'] != null) {
      final brokerAccountName = voucherData['brokerAccount'].toString().toLowerCase().trim();
      if (accountMappings.containsKey(brokerAccountName)) {
        updatedData['brokerAccountId'] = accountMappings[brokerAccountName];
        hasChanges = true;
        log('Mapped broker account: $brokerAccountName -> ${accountMappings[brokerAccountName]}');
      } else {
        warnings.add('Broker account "$brokerAccountName" not found in Chart of Accounts');
      }
    }

    // Migrate munshiana account
    if (voucherData.containsKey('munshianaAccount') && voucherData['munshianaAccount'] != null) {
      final munshianaAccountName = voucherData['munshianaAccount'].toString().toLowerCase().trim();
      if (accountMappings.containsKey(munshianaAccountName)) {
        updatedData['munshianaAccountId'] = accountMappings[munshianaAccountName];
        hasChanges = true;
        log('Mapped munshiana account: $munshianaAccountName -> ${accountMappings[munshianaAccountName]}');
      } else {
        warnings.add('Munshiana account "$munshianaAccountName" not found in Chart of Accounts');
      }
    }

    // Add migration metadata
    if (hasChanges) {
      updatedData['migratedToChartOfAccounts'] = true;
      updatedData['migrationDate'] = DateTime.now().millisecondsSinceEpoch;
    }

    return VoucherMigrationResult(
      hasChanges: hasChanges,
      updatedData: updatedData,
      warnings: warnings,
    );
  }

  /// Update voucher in Firestore
  Future<void> _updateVoucherInFirestore(
    String documentId,
    Map<String, dynamic> updatedData,
    String uid,
  ) async {
    try {
      // Remove the document ID from the data before updating
      final dataToUpdate = Map<String, dynamic>.from(updatedData);
      dataToUpdate.remove('id');

      await _firestore
          .collection(AppCollection.vouchersCollection)
          .doc(documentId)
          .update(dataToUpdate);
    } catch (e) {
      log('Error updating voucher in Firestore: $e');
      rethrow;
    }
  }

  /// Generate migration report
  String generateMigrationReport(MigrationResult result) {
    final buffer = StringBuffer();

    buffer.writeln('=== VOUCHER CHART OF ACCOUNTS MIGRATION REPORT ===');
    buffer.writeln('Generated: ${DateTime.now()}');
    buffer.writeln('');
    buffer.writeln(result.summary);

    if (result.errors.isNotEmpty) {
      buffer.writeln('\nERRORS:');
      for (final error in result.errors) {
        buffer.writeln('- $error');
      }
    }

    if (result.warnings.isNotEmpty) {
      buffer.writeln('\nWARNINGS:');
      for (final warning in result.warnings) {
        buffer.writeln('- $warning');
      }
    }

    if (result.accountMappings.isNotEmpty) {
      buffer.writeln('\nACCOUNT MAPPINGS:');
      for (final mapping in result.accountMappings.entries) {
        buffer.writeln('- "${mapping.key}" -> ${mapping.value}');
      }
    }

    return buffer.toString();
  }

  /// Quick check to see if any vouchers need migration
  Future<Either<FailureObj, bool>> checkMigrationNeeded(String uid) async {
    try {
      log('Checking if voucher migration is needed for company: $uid');

      // Load a sample of vouchers to check
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppCollection.vouchersCollection)
          .where('uid', isEqualTo: uid)
          .limit(10) // Check first 10 vouchers
          .get();

      for (final doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;

        // If any voucher has legacy fields but no Chart of Accounts fields, migration is needed
        if (_hasLegacyAccountFields(data) && !_hasChartOfAccountsFields(data)) {
          log('Migration needed: Found vouchers with legacy account fields');
          return const Right(true);
        }
      }

      log('Migration not needed: All vouchers already migrated or no legacy fields found');
      return const Right(false);
    } catch (e) {
      log('Error checking migration status: $e');
      return Left(FailureObj(
        code: 'migration-check-failed',
        message: 'Failed to check migration status: $e',
      ));
    }
  }

  /// Check if voucher has legacy account fields
  bool _hasLegacyAccountFields(Map<String, dynamic> voucherData) {
    return (voucherData.containsKey('brokerAccount') && voucherData['brokerAccount'] != null) ||
           (voucherData.containsKey('munshianaAccount') && voucherData['munshianaAccount'] != null);
  }

  /// Rollback migration for a specific voucher (remove Chart of Accounts fields)
  Future<Either<FailureObj, String>> rollbackVoucherMigration({
    required String uid,
    required String voucherNumber,
  }) async {
    try {
      log('Rolling back migration for voucher: $voucherNumber');

      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppCollection.vouchersCollection)
          .where('uid', isEqualTo: uid)
          .where('voucherNumber', isEqualTo: voucherNumber)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        return Left(FailureObj(
          code: 'voucher-not-found',
          message: 'Voucher $voucherNumber not found',
        ));
      }

      final doc = querySnapshot.docs.first;
      final data = doc.data() as Map<String, dynamic>;

      // Remove Chart of Accounts fields
      final fieldsToRemove = [
        'brokerAccountId',
        'munshianaAccountId',
        'salesTaxAccountId',
        'freightTaxAccountId',
        'profitAccountId',
        'migratedToChartOfAccounts',
        'migrationDate',
      ];

      final updates = <String, dynamic>{};
      for (final field in fieldsToRemove) {
        updates[field] = FieldValue.delete();
      }

      await doc.reference.update(updates);

      log('Successfully rolled back migration for voucher: $voucherNumber');
      return Right('Migration rolled back successfully for voucher $voucherNumber');
    } catch (e) {
      log('Error rolling back migration for voucher $voucherNumber: $e');
      return Left(FailureObj(
        code: 'rollback-failed',
        message: 'Failed to rollback migration: $e',
      ));
    }
  }
}
